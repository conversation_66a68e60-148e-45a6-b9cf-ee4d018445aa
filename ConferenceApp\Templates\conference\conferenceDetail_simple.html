<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ conference.title }} - Détails</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .conference-detail {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .conference-detail::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .conference-header {
            border-bottom: 2px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .conference-title {
            color: #667eea;
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }

        .conference-category {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            display: inline-block;
            font-size: 0.9em;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .conference-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102,126,234,0.1);
        }

        .info-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .info-value {
            color: #212529;
            font-size: 1.1em;
        }

        .conference-description {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }

        .btn-secondary {
            background: #6c757d;
            box-shadow: 0 4px 15px rgba(108,117,125,0.3);
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108,117,125,0.4);
        }

        .program-link {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 4px 15px rgba(40,167,69,0.3);
        }

        .program-link:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40,167,69,0.4);
        }

        .actions-section {
            text-align: center;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .conference-detail {
                padding: 25px;
                margin: 0 10px;
            }
            
            .conference-info {
                grid-template-columns: 1fr;
            }
        }

        .conference-detail {
            opacity: 0;
            transform: translateY(20px);
            animation: slideUp 0.6s ease forwards;
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👁️ Détails de la Conférence</h1>
            <p>{{ conference.title }}</p>
        </div>

        <div class="conference-detail">
            <div class="conference-header">
                <h1 class="conference-title">{{ conference.title }}</h1>
                <span class="conference-category">{{ conference.category.title }}</span>
            </div>

            <div class="conference-info">
                <div class="info-item">
                    <div class="info-label">📅 Date de début</div>
                    <div class="info-value">{{ conference.start_date|date:"d/m/Y à H:i" }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">📅 Date de fin</div>
                    <div class="info-value">{{ conference.end_date|date:"d/m/Y à H:i" }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">📍 Lieu</div>
                    <div class="info-value">{{ conference.location }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">💰 Prix</div>
                    <div class="info-value">{{ conference.price }} €</div>
                </div>

                <div class="info-item">
                    <div class="info-label">👥 Capacité</div>
                    <div class="info-value">{{ conference.capacity }} participants</div>
                </div>

                <div class="info-item">
                    <div class="info-label">📊 Réservations</div>
                    <div class="info-value">
                        {{ conference.reservation_set.count }} / {{ conference.capacity }}
                        {% if conference.reservation_set.count >= conference.capacity %}
                            <span style="color: red;">(Complet)</span>
                        {% endif %}
                    </div>
                </div>
            </div>

            {% if conference.description %}
            <div class="conference-description">
                <h3>📝 Description</h3>
                <p>{{ conference.description|linebreaks }}</p>
            </div>
            {% endif %}

            {% if conference.program %}
            <div style="margin-bottom: 20px;">
                <h3>📋 Programme</h3>
                <a href="{{ conference.program.url }}" class="btn program-link" target="_blank">
                    📄 Télécharger le programme (PDF)
                </a>
            </div>
            {% endif %}

            <div class="actions-section">
                <a href="{% url 'conference_class_list' %}" class="btn btn-secondary">
                    ← Retour à la liste
                </a>
                
                {% if user.is_staff %}
                    <a href="/admin/ConferenceApp/conference/{{ conference.id }}/change/" class="btn" style="background: #ffc107; color: black;">
                        ✏️ Modifier (Admin)
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
