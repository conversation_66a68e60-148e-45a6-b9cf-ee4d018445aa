# Generated by Django 4.2 on 2025-06-02 18:33

import ConferenceApp.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, validators=[ConferenceApp.models.validate_letters])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
            },
        ),
        migrations.CreateModel(
            name='Conference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=250, validators=[ConferenceApp.models.validate_letters])),
                ('description', models.TextField(blank=True)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now, validators=[ConferenceApp.models.validate_start_date])),
                ('end_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('location', models.CharField(max_length=250)),
                ('price', models.FloatField()),
                ('capacity', models.IntegerField()),
                ('program', models.FileField(upload_to='files/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Reservation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('confirmed', models.BooleanField(default=False)),
                ('reservation_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('conference', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ConferenceApp.conference')),
            ],
        ),
    ]
