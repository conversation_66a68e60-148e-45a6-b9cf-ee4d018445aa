<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Conférences</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }

        .btn-success {
            background: #28a745;
            box-shadow: 0 4px 15px rgba(40,167,69,0.3);
        }

        .btn-success:hover {
            background: #1e7e34;
            box-shadow: 0 6px 20px rgba(40,167,69,0.4);
        }

        .search-box {
            background: white;
            padding: 10px 20px;
            border-radius: 25px;
            border: none;
            outline: none;
            font-size: 16px;
            width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .conferences-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .conference-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .conference-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .conference-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #28a745);
        }

        .conference-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .conference-category {
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            display: inline-block;
            margin-bottom: 15px;
        }

        .conference-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            align-items: center;
            font-size: 0.9em;
            color: #666;
        }

        .info-item i {
            margin-right: 8px;
            width: 16px;
            color: #007bff;
        }

        .conference-price {
            font-size: 1.3em;
            font-weight: bold;
            color: #28a745;
            text-align: center;
            margin-bottom: 15px;
        }

        .conference-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 0.9em;
            border-radius: 20px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .btn-detail {
            background: #17a2b8;
            color: white;
        }

        .btn-detail:hover {
            background: #138496;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .empty-state h3 {
            color: #666;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .empty-state p {
            color: #999;
            margin-bottom: 25px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }

            .conferences-grid {
                grid-template-columns: 1fr;
            }

            .search-box {
                width: 100%;
            }

            .actions {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Conférences Disponibles</h1>
            <p>Découvrez nos événements exceptionnels</p>
        </div>

        <div class="actions">
            <input type="text" class="search-box" placeholder="🔍 Rechercher une conférence..." id="searchInput">
            <a href="{% url 'conference_create' %}" class="btn btn-success">
                ➕ Créer une Conférence
            </a>
        </div>

        <div class="conferences-grid" id="conferencesGrid">
            {% for conference in conferences %}
            <div class="conference-card" data-title="{{ conference.title|lower }}" data-category="{{ conference.category.title|lower }}">
                <h3 class="conference-title">{{ conference.title }}</h3>
                <div class="conference-category">{{ conference.category.title }}</div>

                <div class="conference-info">
                    <div class="info-item">
                        <i>📅</i>
                        <span>{{ conference.start_date|date:"d/m/Y" }}</span>
                    </div>
                    <div class="info-item">
                        <i>🕒</i>
                        <span>{{ conference.start_date|date:"H:i" }}</span>
                    </div>
                    <div class="info-item">
                        <i>📍</i>
                        <span>{{ conference.location }}</span>
                    </div>
                    <div class="info-item">
                        <i>👥</i>
                        <span>{{ conference.capacity }} places</span>
                    </div>
                </div>

                <div class="conference-price">
                    💰 {{ conference.price }} €
                </div>

                <div class="conference-actions">
                    <a href="{% url 'conference_detail' conference.id %}" class="btn-small btn-detail">
                        👁️ Voir Détails
                    </a>
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <h3>🎪 Aucune conférence disponible</h3>
                <p>Il n'y a actuellement aucune conférence programmée.</p>
                <a href="{% url 'conference_create' %}" class="btn btn-success">
                    ➕ Créer la première conférence
                </a>
            </div>
            {% endfor %}
        </div>
    </div>

    <script>
        // Fonction de recherche en temps réel
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const cards = document.querySelectorAll('.conference-card');

            cards.forEach(card => {
                const title = card.getAttribute('data-title');
                const category = card.getAttribute('data-category');

                if (title.includes(searchTerm) || category.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // Animation d'apparition des cartes
        window.addEventListener('load', function() {
            const cards = document.querySelectorAll('.conference-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 50);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
