<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>Liste des Conférences</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
      }

      .header {
        text-align: center;
        margin-bottom: 40px;
        color: white;
        position: relative;
      }

      .user-info {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        align-items: center;
        gap: 15px;
        font-size: 0.9em;
      }

      .btn-logout {
        background: rgba(255,255,255,0.2);
        color: white;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 1px solid rgba(255,255,255,0.3);
      }

      .btn-logout:hover {
        background: rgba(255,255,255,0.3);
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
      }

      .header h1 {
        font-size: 3em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .header p {
        font-size: 1.2em;
        opacity: 0.9;
        margin-bottom: 20px;
      }

      .btn-create {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 15px 30px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1em;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        transition: all 0.3s ease;
        margin-top: 10px;
      }

      .btn-create:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        background: linear-gradient(45deg, #20c997, #28a745);
      }

      table {
        width: 100%;
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border-collapse: collapse;
        margin-top: 20px;
      }

      thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      th {
        padding: 20px 15px;
        text-align: left;
        font-weight: 600;
        font-size: 1.1em;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
      }

      td {
        padding: 15px;
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
        border: none;
      }

      tr:hover {
        background-color: #f8f9fa;
        transform: scale(1.01);
        transition: all 0.3s ease;
      }

      tr:last-child td {
        border-bottom: none;
      }

      a {
        text-decoration: none;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-block;
        margin: 2px;
      }

      a[href*="detail"] {
        background: #17a2b8;
        color: white;
      }

      a[href*="detail"]:hover {
        background: #138496;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
      }

      a[href*="create"] {
        background: #28a745;
        color: white;
      }

      a[href*="create"]:hover {
        background: #1e7e34;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
      }

      a[href*="delete"] {
        background: #dc3545;
        color: white;
      }

      a[href*="delete"]:hover {
        background: #c82333;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
      }

      .price {
        font-weight: bold;
        color: #28a745;
        font-size: 1.1em;
      }

      .category {
        background: #007bff;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.9em;
        display: inline-block;
      }

      .date {
        color: #666;
        font-size: 0.95em;
      }

      .empty-message {
        text-align: center;
        padding: 40px;
        color: #666;
        font-style: italic;
        font-size: 1.2em;
      }

      .actions-cell {
        text-align: center;
        white-space: nowrap;
      }

      .btn-update {
        background: linear-gradient(45deg, #ffc107, #ff8f00);
        color: #212529;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 20px;
        font-size: 0.85em;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(255,193,7,0.3);
        display: inline-block;
        margin-right: 5px;
      }

      .btn-update:hover {
        background: linear-gradient(45deg, #ff8f00, #ffc107);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255,193,7,0.4);
        color: #212529;
        text-decoration: none;
      }

      @media (max-width: 768px) {
        .header h1 {
          font-size: 2em;
        }

        table {
          font-size: 0.9em;
        }

        th,
        td {
          padding: 10px 8px;
        }

        a {
          padding: 6px 12px;
          font-size: 0.85em;
        }
      }

      /* Animation d'apparition */
      table {
        opacity: 0;
        transform: translateY(20px);
        animation: slideUp 0.6s ease forwards;
      }

      @keyframes slideUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="user-info">
          {% if user.is_authenticated %}
            <span>👋 Bonjour {{ user.first_name }} {{ user.last_name }}</span>
            <a href="{% url 'logout' %}" class="btn-logout">🚪 Déconnexion</a>
          {% else %}
            <a href="{% url 'login' %}" class="btn-logout">🔐 Connexion</a>
            <a href="{% url 'register' %}" class="btn-logout">� Inscription</a>
          {% endif %}
        </div>
        <h1>🎯 Gestion des Conférences</h1>
        <p>Tableau de bord administrateur</p>
        <a href="{% url 'conference_create' %}" class="btn-create">
          ➕ Créer une Nouvelle Conférence
        </a>
      </div>

      <table border="1">
        <thead>
          <tr>
            <th>titre</th>
            <th>category</th>
            <th>Date_debut</th>
            <th>date_fin</th>
            <th>prix</th>
            <th>update</th>
            <th>detail</th>
            <th>delete</th>
            <th>reserver</th>
          </tr>
        </thead>
        <tbody>
          {% for conference in conferences %}
          <tr>
            <td>{{ conference.title }}</td>
            <td>{{ conference.category.title }}</td>
            <td>{{ conference.start_date }}</td>
            <td>{{ conference.end_date }}</td>
            <td>{{ conference.price }} €</td>
            <td>
              <a href="{% url 'conference_detail' conference.id %}">Detail</a>
            </td>

            <td>
              <a
                href="{% url 'conference_delete' conference.id %}"
                style="color: red"
                >Delete</a
              >
            </td>
            
            <td>
              <a
                href="{% url 'conference_update' conference.id %}"
                class="btn-update"
                title="Modifier cette conférence"
                >Update</a
              >
            </td>
               <td>
                {% if user.is_authenticated %}
                {% if conference.id in user_reservations %}
                <p>Deja réserver</p>
                {% else %}
                <form action="{% url 'conference_reserve' conference.id %}" method="POST">
                    {% csrf_token %}
                    <button type="submit">Reserver</button>
                </form>
                {% endif %}
                {% endif %}
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="4">No conferences available.</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
</body>
</html>
    </div>
  </body>
</html>
