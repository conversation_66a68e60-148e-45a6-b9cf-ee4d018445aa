#!/usr/bin/env python
"""
Script simple pour créer un utilisateur admin
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from django.contrib.auth import get_user_model

def create_admin():
    User = get_user_model()
    
    # Supprimer l'ancien utilisateur admin s'il existe
    try:
        old_admin = User.objects.get(username='admin')
        old_admin.delete()
        print("🗑️  Ancien utilisateur admin supprimé")
    except User.DoesNotExist:
        pass
    
    # Créer le nouvel utilisateur admin
    try:
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            cin='12345678',
            first_name='Admin',
            last_name='User'
        )
        
        print("✅ Nouvel utilisateur admin créé avec succès!")
        print("🌐 URL d'administration: http://127.0.0.1:8000/admin/")
        print("👤 Username: admin")
        print("🔑 Password: admin123")
        print("📧 Email: <EMAIL>")
        print("🆔 CIN: 12345678")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")

if __name__ == "__main__":
    create_admin()
