# Generated by Django 5.2.1 on 2025-06-02 17:53

import UserApp.models
import django.contrib.auth.models
import django.core.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Participant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('cin', models.CharField(max_length=8, unique=True, validators=[django.core.validators.RegexValidator('^\\d{8}$', 'This field must contain exactly 8 digits')])),
                ('email', models.EmailField(max_length=254, unique=True, validators=[UserApp.models.email_validator])),
                ('first_name', models.CharField(max_length=250, validators=[UserApp.models.validate_letters])),
                ('last_name', models.CharField(max_length=250, validators=[UserApp.models.validate_letters])),
                ('username', models.CharField(max_length=150, unique=True)),
                ('participant_category', models.CharField(choices=[('ETUDIANT', 'Etudiant'), ('CHERCHEUR', 'Chercheur'), ('ENSEIGNANT', 'Enseignant'), ('DOCTEUR', 'Docteur')], max_length=100, verbose_name='category')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
