from django.urls import reverse_lazy
from django.views.generic import C<PERSON><PERSON><PERSON><PERSON>, <PERSON>ginView, LogoutView
from django.contrib.auth.forms import UserCreationForm
from django.contrib import messages
from .models import Participant
from django import forms

# Formulaire simple pour l'inscription
class ParticipantCreationForm(UserCreationForm):
    email = forms.EmailField(required=True)
    first_name = forms.CharField(max_length=250, required=True)
    last_name = forms.CharField(max_length=250, required=True)
    cin = forms.CharField(max_length=8, required=True)
    participant_category = forms.ChoiceField(choices=Participant.CHOICE, required=True)

    class Meta:
        model = Participant
        fields = ('username', 'first_name', 'last_name', 'email', 'cin', 'participant_category', 'password1', 'password2')

# Create your views here.
class UserCreateView(CreateView):
    model = Participant
    form_class = ParticipantCreationForm
    template_name = 'users/register.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        messages.success(self.request, 'Compte créé avec succès ! Vous pouvez maintenant vous connecter.')
        return super().form_valid(form)

class LoginCustom(LoginView):
    template_name = 'registration/login.html'

    def get_success_url(self):
        messages.success(self.request, f'Bienvenue {self.request.user.first_name} !')
        return reverse_lazy('conference_class_list')

class logoutCustom(LogoutView):
    next_page = reverse_lazy('login')

    def dispatch(self, request, *args, **kwargs):
        messages.info(request, 'Vous avez été déconnecté avec succès.')
        return super().dispatch(request, *args, **kwargs)
