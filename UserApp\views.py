from django.urls import reverse_lazy
from django.views.generic import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gin<PERSON>iew, <PERSON>goutView
from .models import Participant
from .forms import ParticipantCreationForm

# Create your views here.
class UserCreateView(CreateView):
    model = Participant
    form_class = ParticipantCreationForm
    template_name = 'users/register.html'
    success_url = reverse_lazy('login')
class LoginCustom(LoginView):
    template_name = 'users/login.html'
    def get_success_url(self):
        return reverse_lazy('conference_class_list')
class logoutCustom(LogoutView):
    template_name = 'users/login.html'
    next_page = reverse_lazy('login')
