<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Create Conference</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      h1 {
        color: #007bff;
        margin: 0 0 20px 0;
        font-size: 2.5em;
      }
      form {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      label {
        display: block;
        margin-bottom: 10px;
        font-weight: bold;
      }
      input[type="text"],
      input[type="number"],
      input[type="date"],
      select,
      textarea {
        width: 100%;
        padding: 10px;
        margin-bottom: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        font-size: 1em;
      }
      button {
        background: #007bff;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 5px;
        font-size: 1.2em;
        cursor: pointer;
        transition: background 0.3s;
      }
      button:hover {
        background: #0056b3;
      }
    </style>
  </head>
  <body>
    <h1>Create Conference</h1>
    <form
      method="post"
      enctype="multipart/form-data">
      {% csrf_token %} {{ form.as_p }}
      <button type="submit">Create</button>
    </form>
  </body>
</html>
