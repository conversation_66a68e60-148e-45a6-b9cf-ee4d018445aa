<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>Conference Form</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      h1 {
        text-align: center;
        color: white;
        font-size: 3em;
        margin-bottom: 40px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      form {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
      }

      form::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, #667eea, #764ba2);
      }

      form p {
        margin-bottom: 25px;
      }

      form label {
        display: block;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        font-size: 1.1em;
      }

      form input,
      form select,
      form textarea {
        width: 100%;
        padding: 15px;
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        font-size: 1em;
        transition: all 0.3s ease;
        background: #f8f9fa;
        font-family: inherit;
      }

      form input:focus,
      form select:focus,
      form textarea:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
      }

      form textarea {
        resize: vertical;
        min-height: 120px;
      }

      button {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 25px;
        font-size: 1.1em;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        margin-top: 20px;
        display: block;
        margin-left: auto;
        margin-right: auto;
      }

      button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      a {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: white;
        text-decoration: none;
        font-weight: 500;
        padding: 10px 20px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        max-width: 200px;
        margin-left: auto;
        margin-right: auto;
        transition: all 0.3s ease;
      }

      a:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      .errorlist {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 5px;
        list-style: none;
      }

      @media (max-width: 768px) {
        h1 {
          font-size: 2em;
        }

        form {
          padding: 25px;
          margin: 0 10px;
        }
      }

      form {
        opacity: 0;
        transform: translateY(20px);
        animation: slideUp 0.6s ease forwards;
      }

      @keyframes slideUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <h1>Create Conference</h1>
    <form method="post">
      {% csrf_token %} {{ form.as_p }}
      <button type="submit">Create</button>
    </form>
    <a href="{% url 'conference_class_list' %}">Back to list</a>
  </body>
</html>
