#!/usr/bin/env python
"""
Script pour réinitialiser le mot de passe admin Django
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist

def reset_admin_password():
    """Réinitialise le mot de passe d'un utilisateur admin"""
    
    User = get_user_model()
    
    print("🔧 Script de réinitialisation du mot de passe admin")
    print("=" * 50)
    
    # Afficher tous les superutilisateurs existants
    superusers = User.objects.filter(is_superuser=True)
    
    if superusers.exists():
        print("👥 Superutilisateurs existants :")
        for i, user in enumerate(superusers, 1):
            print(f"   {i}. {user.username} ({user.email})")
        print()
        
        # Demander quel utilisateur modifier
        try:
            choice = input("Entrez le numéro de l'utilisateur à modifier (ou 'new' pour créer un nouveau) : ").strip()
            
            if choice.lower() == 'new':
                create_new_superuser()
            else:
                user_index = int(choice) - 1
                if 0 <= user_index < len(superusers):
                    user = superusers[user_index]
                    change_password(user)
                else:
                    print("❌ Numéro invalide!")
        except (ValueError, KeyboardInterrupt):
            print("\n❌ Opération annulée.")
    else:
        print("⚠️  Aucun superutilisateur trouvé.")
        create_new_superuser()

def change_password(user):
    """Change le mot de passe d'un utilisateur"""
    print(f"\n🔑 Changement du mot de passe pour : {user.username}")
    
    try:
        new_password = input("Nouveau mot de passe : ").strip()
        if len(new_password) < 3:
            print("❌ Le mot de passe doit contenir au moins 3 caractères.")
            return
            
        user.set_password(new_password)
        user.save()
        
        print(f"✅ Mot de passe changé avec succès pour {user.username}!")
        print(f"🌐 Vous pouvez maintenant vous connecter sur http://127.0.0.1:8000/admin/")
        print(f"   Username: {user.username}")
        print(f"   Password: {new_password}")
        
    except KeyboardInterrupt:
        print("\n❌ Opération annulée.")

def create_new_superuser():
    """Crée un nouveau superutilisateur"""
    print("\n👤 Création d'un nouveau superutilisateur")
    
    try:
        User = get_user_model()
        
        username = input("Username : ").strip()
        if not username:
            print("❌ Le nom d'utilisateur ne peut pas être vide.")
            return
            
        email = input("Email : ").strip()
        if not email:
            print("❌ L'email ne peut pas être vide.")
            return
            
        password = input("Mot de passe : ").strip()
        if len(password) < 3:
            print("❌ Le mot de passe doit contenir au moins 3 caractères.")
            return
        
        # Créer l'utilisateur
        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password
        )
        
        print(f"✅ Superutilisateur créé avec succès!")
        print(f"🌐 Vous pouvez maintenant vous connecter sur http://127.0.0.1:8000/admin/")
        print(f"   Username: {username}")
        print(f"   Password: {password}")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création : {e}")
    except KeyboardInterrupt:
        print("\n❌ Opération annulée.")

if __name__ == "__main__":
    reset_admin_password()
