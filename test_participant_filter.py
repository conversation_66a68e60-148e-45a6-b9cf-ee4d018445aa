#!/usr/bin/env python
"""
Script de test pour le filtre de participants
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from ConferenceApp.models import Category, Conference, Reservation
from UserApp.models import Participant

def create_test_data():
    """Crée des données de test pour les réservations"""
    
    # Créer une catégorie de test
    category, created = Category.objects.get_or_create(
        title="Test Category",
        defaults={'title': "Test Category"}
    )
    
    # Créer une conférence de test
    conference, created = Conference.objects.get_or_create(
        title="Test Conference",
        defaults={
            'title': "Test Conference",
            'description': "Test description",
            'location': "Test location",
            'price': 100.0,
            'capacity': 50,
            'category': category
        }
    )
    
    # Créer un participant de test
    participant, created = Participant.objects.get_or_create(
        cin="87654321",
        defaults={
            'username': "testuser",
            'email': "<EMAIL>",
            'cin': "87654321",
            'first_name': "Test",
            'last_name': "User"
        }
    )
    
    # Supprimer les anciennes réservations de test
    Reservation.objects.filter(conference__title="Test Conference").delete()
    
    # Créer des réservations de test
    reservations = []
    
    # Réservation avec participant
    reservation_with_participant = Reservation.objects.create(
        conference=conference,
        participant=participant,
        confirmed=True
    )
    reservations.append(("With Participant", reservation_with_participant))
    
    # Réservation sans participant (théoriquement impossible avec le modèle actuel,
    # mais on peut tester la logique du filtre)
    print("✅ Données de test créées")
    print(f"   - Conférence: {conference.title}")
    print(f"   - Participant: {participant.username}")
    print(f"   - Réservations créées: {len(reservations)}")
    
    return reservations

def test_participant_filter():
    """Teste le filtre de participants"""
    
    print("🧪 Test du filtre de participants")
    print("=" * 50)
    
    # Créer des données de test
    reservations = create_test_data()
    
    # Tester le filtre "with_participant"
    print("\n🔍 Test du filtre: with_participant")
    print("-" * 40)
    
    reservations_with_participant = Reservation.objects.filter(participant__isnull=False)
    print(f"Réservations avec participant: {reservations_with_participant.count()}")
    
    for reservation in reservations_with_participant:
        print(f"  ✅ Réservation ID: {reservation.id}")
        print(f"     Participant: {reservation.participant.username}")
        print(f"     Conférence: {reservation.conference.title}")
        print(f"     Confirmée: {reservation.confirmed}")
    
    # Tester le filtre "no_participant"
    print("\n🔍 Test du filtre: no_participant")
    print("-" * 40)
    
    reservations_without_participant = Reservation.objects.filter(participant__isnull=True)
    print(f"Réservations sans participant: {reservations_without_participant.count()}")
    
    if reservations_without_participant.exists():
        for reservation in reservations_without_participant:
            print(f"  ❌ Réservation ID: {reservation.id}")
            print(f"     Participant: None")
            print(f"     Conférence: {reservation.conference.title}")
    else:
        print("  ℹ️  Aucune réservation sans participant (normal avec le modèle actuel)")
    
    # Statistiques
    print(f"\n📊 Statistiques:")
    total_reservations = Reservation.objects.count()
    with_participant = Reservation.objects.filter(participant__isnull=False).count()
    without_participant = Reservation.objects.filter(participant__isnull=True).count()
    
    print(f"   Total réservations: {total_reservations}")
    print(f"   Avec participant: {with_participant}")
    print(f"   Sans participant: {without_participant}")

def test_filter_logic():
    """Teste la logique du filtre"""
    
    print("\n🎯 Test de la logique du filtre ParticipantFilter")
    print("=" * 50)
    
    print("Logique du filtre:")
    print("  - with_participant: participant__isnull=False")
    print("  - no_participant: participant__isnull=True")
    
    # Test de la logique
    all_reservations = Reservation.objects.all()
    print(f"\nToutes les réservations: {all_reservations.count()}")
    
    # Simuler le filtre
    for filter_value in ['with_participant', 'no_participant']:
        print(f"\n🔍 Simulation du filtre: {filter_value}")
        
        if filter_value == 'with_participant':
            filtered = all_reservations.filter(participant__isnull=False)
        elif filter_value == 'no_participant':
            filtered = all_reservations.filter(participant__isnull=True)
        
        print(f"   Résultats: {filtered.count()} réservations")

if __name__ == "__main__":
    test_participant_filter()
    test_filter_logic()
