#!/usr/bin/env python
"""
Script de test pour valider MinLengthValidator sur les titres de conférence
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from ConferenceApp.models import Conference, Category
from django.core.exceptions import ValidationError

def test_minlength_validation():
    print("🧪 Test de MinLengthValidator pour les titres de conférence")
    print("=" * 60)
    
    # Créer une catégorie pour les tests
    try:
        category = Category.objects.create(title="Test Category")
        print("✅ Catégorie de test créée")
    except Exception as e:
        print(f"⚠️  Utilisation d'une catégorie existante: {e}")
        category = Category.objects.first()
    
    # Tests qui doivent PASSER (≥5 caractères)
    valid_titles = [
        "Python Conference",      # 17 caractères ✅
        "Django Summit",          # 13 caractères ✅
        "AI Workshop",           # 11 caractères ✅
        "Tech Event",            # 10 caractères ✅
        "Hello",                 # 5 caractères exactement ✅
        "ABCDE",                 # 5 caractères ✅
    ]
    
    # Tests qui doivent ÉCHOUER (<5 caractères)
    invalid_titles = [
        "AI",                    # 2 caractères ❌
        "Tech",                  # 4 caractères ❌
        "A",                     # 1 caractère ❌
        "",                      # 0 caractère ❌
    ]
    
    print("\n✅ Tests qui doivent PASSER (≥5 caractères) :")
    for title in valid_titles:
        try:
            # Tester la validation du modèle
            conference = Conference(
                title=title,
                description="Test description",
                location="Test location",
                price=100.0,
                capacity=50,
                category=category
            )
            conference.full_clean()  # Déclenche la validation
            print(f"   ✅ '{title}' ({len(title)} chars) - VALIDE")
        except ValidationError as e:
            print(f"   ❌ '{title}' ({len(title)} chars) - ERREUR: {e}")
    
    print("\n❌ Tests qui doivent ÉCHOUER (<5 caractères) :")
    for title in invalid_titles:
        try:
            conference = Conference(
                title=title,
                description="Test description",
                location="Test location",
                price=100.0,
                capacity=50,
                category=category
            )
            conference.full_clean()  # Déclenche la validation
            print(f"   ❌ '{title}' ({len(title)} chars) - ERREUR: Devrait être invalide!")
        except ValidationError as e:
            print(f"   ✅ '{title}' ({len(title)} chars) - CORRECTEMENT REJETÉ: {e}")
    
    # Tests avec caractères spéciaux (doivent échouer à cause de validate_letters)
    print("\n🔤 Tests avec caractères non-lettres (doivent échouer) :")
    special_titles = [
        "123 Conference",        # Contient des chiffres ❌
        "Tech@Event",           # Contient des caractères spéciaux ❌
        "Hello-World",          # Contient un tiret ❌
    ]
    
    for title in special_titles:
        try:
            conference = Conference(
                title=title,
                description="Test description",
                location="Test location",
                price=100.0,
                capacity=50,
                category=category
            )
            conference.full_clean()
            print(f"   ❌ '{title}' - ERREUR: Devrait être invalide!")
        except ValidationError as e:
            print(f"   ✅ '{title}' - CORRECTEMENT REJETÉ: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Test terminé !")
    print("📝 Résumé:")
    print("   - MinLengthValidator(5) : Vérifie la longueur minimale")
    print("   - validate_letters : Vérifie que seules les lettres et espaces sont autorisés")

if __name__ == "__main__":
    test_minlength_validation()
