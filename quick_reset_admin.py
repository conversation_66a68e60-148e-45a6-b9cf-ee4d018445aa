#!/usr/bin/env python
"""
Script rapide pour réinitialiser un utilisateur admin Django
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from django.contrib.auth import get_user_model

def quick_reset_admin():
    """Réinitialise rapidement l'utilisateur admin"""
    
    User = get_user_model()
    
    print("🔄 Réinitialisation rapide de l'utilisateur admin")
    print("=" * 50)
    
    # Paramètres par défaut
    username = "admin"
    email = "<EMAIL>"
    password = "admin123"
    
    try:
        # Supprimer l'ancien utilisateur admin s'il existe
        try:
            old_user = User.objects.get(username=username)
            old_user.delete()
            print(f"🗑️  Ancien utilisateur '{username}' supprimé.")
        except User.DoesNotExist:
            pass
        
        # C<PERSON>er le nouvel utilisateur admin
        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password,
            first_name="Admin",
            last_name="User",
            cin="12345678"  # CIN par défaut
        )
        
        print(f"✅ Utilisateur admin réinitialisé avec succès!")
        print(f"🌐 Connexion: http://127.0.0.1:8000/admin/")
        print(f"👤 Username: {username}")
        print(f"📧 Email: {email}")
        print(f"🔑 Password: {password}")
        print()
        print("💡 Conseil: Changez le mot de passe après la première connexion!")
        
    except Exception as e:
        print(f"❌ Erreur lors de la réinitialisation : {e}")

def custom_reset_admin():
    """Réinitialise avec des paramètres personnalisés"""
    
    User = get_user_model()
    
    print("🔄 Réinitialisation personnalisée de l'utilisateur admin")
    print("=" * 50)
    
    try:
        username = input("👤 Username (défaut: admin) : ").strip() or "admin"
        email = input("📧 Email (défaut: <EMAIL>) : ").strip() or "<EMAIL>"
        password = input("🔑 Password (défaut: admin123) : ").strip() or "admin123"
        
        if not email.endswith('@esprit.tn'):
            print("❌ L'email doit se terminer par @esprit.tn")
            return
        
        # Supprimer l'ancien utilisateur s'il existe
        try:
            old_user = User.objects.get(username=username)
            old_user.delete()
            print(f"🗑️  Ancien utilisateur '{username}' supprimé.")
        except User.DoesNotExist:
            pass
        
        # Créer le nouvel utilisateur
        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password,
            cin="87654321"  # CIN par défaut
        )
        
        print(f"✅ Utilisateur admin créé avec succès!")
        print(f"🌐 Connexion: http://127.0.0.1:8000/admin/")
        print(f"👤 Username: {username}")
        print(f"📧 Email: {email}")
        print(f"🔑 Password: {password}")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création : {e}")

if __name__ == "__main__":
    print("🔧 Script de réinitialisation admin Django")
    print("=" * 40)
    print("1. 🚀 Réinitialisation rapide (admin/admin123)")
    print("2. ⚙️  Réinitialisation personnalisée")
    
    choice = input("\n➤ Choisissez (1 ou 2) : ").strip()
    
    if choice == "1":
        quick_reset_admin()
    elif choice == "2":
        custom_reset_admin()
    else:
        print("❌ Choix invalide. Utilisation de la réinitialisation rapide.")
        quick_reset_admin()
