<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ conference.title }} - <PERSON><PERSON><PERSON> de la Conférence</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .conference-detail {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .conference-header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .conference-title {
            color: #007bff;
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        .conference-category {
            background: #007bff;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
            font-size: 0.9em;
        }
        .conference-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .info-value {
            color: #212529;
            font-size: 1.1em;
        }
        .conference-description {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.8em;
        }
        .status-upcoming { background: #28a745; color: white; }
        .status-ongoing { background: #ffc107; color: black; }
        .status-past { background: #6c757d; color: white; }
        .status-today { background: #17a2b8; color: white; }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 10px 10px 0;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .program-link {
            background: #28a745;
        }
        .program-link:hover {
            background: #1e7e34;
        }
        @media (max-width: 600px) {
            .conference-info {
                grid-template-columns: 1fr;
            }
            .conference-title {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="conference-detail">
        <!-- En-tête de la conférence -->
        <div class="conference-header">
            <h1 class="conference-title">{{ conference.title }}</h1>
            <span class="conference-category">{{ conference.category.title }}</span>
            
            <!-- Badge de statut -->
            {% now "Y-m-d" as today %}
            {% if conference.start_date|date:"Y-m-d" > today %}
                <span class="status-badge status-upcoming">À venir</span>
            {% elif conference.start_date|date:"Y-m-d" <= today and conference.end_date|date:"Y-m-d" >= today %}
                {% if conference.start_date|date:"Y-m-d" == today %}
                    <span class="status-badge status-today">Aujourd'hui</span>
                {% else %}
                    <span class="status-badge status-ongoing">En cours</span>
                {% endif %}
            {% else %}
                <span class="status-badge status-past">Terminée</span>
            {% endif %}
        </div>

        <!-- Informations principales -->
        <div class="conference-info">
            <div class="info-item">
                <div class="info-label">📅 Date de début</div>
                <div class="info-value">{{ conference.start_date|date:"d/m/Y à H:i" }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">📅 Date de fin</div>
                <div class="info-value">{{ conference.end_date|date:"d/m/Y à H:i" }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">📍 Lieu</div>
                <div class="info-value">{{ conference.location }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">💰 Prix</div>
                <div class="info-value">{{ conference.price }} €</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">👥 Capacité</div>
                <div class="info-value">{{ conference.capacity }} participants</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">📊 Réservations</div>
                <div class="info-value">
                    {{ conference.reservation_set.count }} / {{ conference.capacity }}
                    {% if conference.reservation_set.count >= conference.capacity %}
                        <span style="color: red;">(Complet)</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Description -->
        {% if conference.description %}
        <div class="conference-description">
            <h3>📝 Description</h3>
            <p>{{ conference.description|linebreaks }}</p>
        </div>
        {% endif %}

        <!-- Programme (si disponible) -->
        {% if conference.program %}
        <div style="margin-bottom: 20px;">
            <h3>📋 Programme</h3>
            <a href="{{ conference.program.url }}" class="btn program-link" target="_blank">
                📄 Télécharger le programme (PDF)
            </a>
        </div>
        {% endif %}

        <!-- Informations de création/modification -->
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; font-size: 0.9em;">
            <p>
                <strong>Créée le :</strong> {{ conference.created_at|date:"d/m/Y à H:i" }}<br>
                <strong>Modifiée le :</strong> {{ conference.updated_at|date:"d/m/Y à H:i" }}
            </p>
        </div>

        <!-- Boutons d'action -->
        <div style="margin-top: 30px; text-align: center;">
            <a href="{% url 'conference_list' %}" class="btn btn-secondary">
                ← Retour à la liste
            </a>
            
            {% if conference.start_date|date:"Y-m-d" >= today and conference.reservation_set.count < conference.capacity %}
                <a href="#" class="btn">
                    ✅ Réserver une place
                </a>
            {% endif %}
            
            {% if user.is_staff %}
                <a href="/admin/ConferenceApp/conference/{{ conference.id }}/change/" class="btn" style="background: #ffc107; color: black;">
                    ✏️ Modifier (Admin)
                </a>
            {% endif %}
        </div>
    </div>
</body>
</html>
