html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> de la Conférence</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
      }

      .header {
        text-align: center;
        margin-bottom: 40px;
        color: white;
      }

      .header h1 {
        font-size: 3em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .header p {
        font-size: 1.2em;
        opacity: 0.9;
      }

      .conference-detail {
        background: white;
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
      }

      .conference-detail::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, #667eea, #764ba2);
      }

      .conference-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30px;
      }

      .conference-title {
        font-size: 2.5em;
        margin: 0;
        color: #333;
      }

      .conference-category {
        background: #007bff;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9em;
        display: inline-block;
      }

      .conference-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
      }

      .info-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .info-label {
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
      }

      .info-value {
        color: #212529;
        font-size: 1.1em;
      }

      .conference-description {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
      }

      .conference-description h3 {
        font-size: 1.5em;
        margin: 0 0 10px 0;
        color: #333;
      }

      .conference-description p {
        line-height: 1.5;
        color: #555;
      }

      .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        text-transform: uppercase;
        font-size: 0.8em;
        margin-top: 10px;
      }

      .status-upcoming {
        background: #28a745;
        color: white;
      }

      .status-ongoing {
        background: #ffc107;
        color: black;
      }

      .status-past {
        background: #6c757d;
        color: white;
      }

      .status-today {
        background: #17a2b8;
        color: white;
      }

      .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 24px;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-size: 1.1em;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        margin-top: 20px;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
      }

      .btn-secondary {
        background: #6c757d;
      }

      .btn-secondary:hover {
        background: #545b62;
      }

      .program-link {
        background: #28a745;
        color: white;
      }

      .program-link:hover {
        background: #1e7e34;
      }

      @media (max-width: 768px) {
        .conference-detail {
          padding: 30px;
        }

        .conference-header {
          flex-direction: column;
          gap: 20px;
        }

        .conference-info {
          grid-template-columns: 1fr;
        }

        .btn {
          width: 100%;
          margin: 10px 0;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Détails de la Conférence</h1>
        <p>Informations complètes sur la conférence</p>
      </div>

      <div class="conference-detail">
        <div class="conference-header">
          <h1 class="conference-title">{{ conference.title }}</h1>
          <span class="conference-category">{{ conference.category.title }}</span>
        </div>

        <div class="conference-info">
          <div class="info-item">
            <div class="info-label">📅 Date de début</div>
            <div class="info-value">{{ conference.start_date|date:"d/m/Y à H:i" }}</div>
          </div>

          <div class="info-item">
            <div class="info-label">📅 Date de fin</div>
            <div class="info-value">{{ conference.end_date|date:"d/m/Y à H:i" }}</div>
          </div>

          <div class="info-item">
            <div class="info-label">📍 Lieu</div>
            <div class="info-value">{{ conference.location }}</div>
          </div>

          <div class="info-item">
            <div class="info-label">💰 Prix</div>
            <div class="info-value">{{ conference.price }} €</div>
          </div>

          <div class="info-item">
            <div class="info-label">👥 Capacité</div>
            <div class="info-value">{{ conference.capacity }} participants</div>
          </div>

          <div class="info-item">
            <div class="info-label">📊 Réservations</div>
            <div class="info-value">
              {{ conference.reservation_set.count }} / {{ conference.capacity }}
              {% if conference.reservation_set.count >= conference.capacity %}
                <span style="color: red;">(Complet)</span>
              {% endif %}
            </div>
          </div>
        </div>

        <div class="conference-description">
          <h3>📝 Description</h3>
          <p>{{ conference.description|linebreaks }}</p>
        </div>

        <div class="conference-program">
          <h3>📋 Programme</h3>
          {% if conference.program %}
            <a href="{{ conference.program.url }}" class="btn program-link" target="_blank">
              📄 Télécharger le programme (PDF)
            </a>
          {% else %}
            <p>Aucun programme disponible.</p>
          {% endif %}
        </div>

        <div class="conference-actions">
          <a href="{% url 'conference_class_list' %}" class="btn btn-secondary">
            ← Retour à la liste




















