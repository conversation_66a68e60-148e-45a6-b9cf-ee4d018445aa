from django.shortcuts import render
from .models import Conference
from django.views.generic import ListView, DetailView, CreateView, DeleteView, UpdateView
from django.urls import reverse_lazy

def conferenceList(request):
    list = Conference.objects.all()
    return render(request, 'conference/conferenceList.html', {'conferences': list})
class ConferenceListView(ListView):
    model = Conference
    template_name = 'conference/conferenceList.html'
    context_object_name = 'conferences'
    def get_queryset(self):
        return Conference.objects.all().order_by('start_date')

class ConferenceDetailView(DetailView):
    model = Conference
    template_name = 'conference/conferenceDetail.html'
    context_object_name = 'conference'
class CreateView(CreateView):
    model = Conference
    template_name = 'conference/conference_form.html'
    fields = ['title', 'description', 'start_date', 'end_date', 'location', 'price', 'capacity', 'program', 'category']
    success_url = reverse_lazy('conference_class_list')
class DeleteView(DeleteView):
    model = Conference
    template_name = 'conference/conference_delete.html'
    success_url = reverse_lazy('conference_class_list')
class UpdateView(UpdateView):
    model = Conference
    template_name = 'conference/conference_form.html'
    fields = ['title', 'description', 'start_date', 'end_date', 'location', 'price', 'capacity', 'program', 'category']
    success_url = reverse_lazy('conference_class_list')
