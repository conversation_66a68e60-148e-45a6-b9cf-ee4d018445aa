from django.shortcuts import render
from .models import Conference
from django.views.generic import ListView ,DetailView
def conferenceList(request):
    list = Conference.objects.all()
    return render(request, 'conference/conferenceList.html', {'conferences': list})
class ConferenceListView(ListView):
    model = Conference
    template_name = 'conference/conferenceList.html'
    context_object_name = 'conferences'
    def get_queryset(self):
        return Conference.objects.all().order_by('start_date')

class ConferenceDetailView(DetailView):
    model = Conference
    template_name = 'conference/conferenceDetail.html'
    context_object_name = 'conference'
