from django.shortcuts import render
from django.views.generic import ListView
from django.utils import timezone
from .models import Conference, Category

def conferenceList(request):
    """Vue fonction pour la liste des conférences"""
    conferences = Conference.objects.all()
    return render(request, 'conference/conferenceList.html', {'conferences': conferences})

class ConferenceListView(ListView):
    """Vue classe pour la liste des conférences avec filtrage avancé"""
    model = Conference
    template_name = 'ConferenceApp/conference_list.html'
    context_object_name = 'conferences'
    paginate_by = 10
    ordering = ['start_date', 'title']

    def get_queryset(self):
        """Retourne les conférences avec filtrage optionnel"""
        queryset = super().get_queryset()

        # Filtrer par statut si spécifié dans les paramètres GET
        status = self.request.GET.get('status')
        today = timezone.now().date()

        if status == 'upcoming':
            queryset = queryset.filter(start_date__gt=today)
        elif status == 'ongoing':
            queryset = queryset.filter(start_date__lte=today, end_date__gte=today)
        elif status == 'past':
            queryset = queryset.filter(end_date__lt=today)
        elif status == 'today':
            queryset = queryset.filter(start_date=today)

        # Filtrer par catégorie si spécifiée
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__id=category)

        # Recherche par titre ou lieu
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                title__icontains=search
            ) | queryset.filter(
                location__icontains=search
            )

        return queryset

    def get_context_data(self, **kwargs):
        """Ajoute des données supplémentaires au contexte"""
        context = super().get_context_data(**kwargs)

        # Ajouter les catégories pour le filtre
        context['categories'] = Category.objects.all()

        # Ajouter les paramètres actuels pour le template
        context['current_status'] = self.request.GET.get('status', 'all')
        context['current_category'] = self.request.GET.get('category', '')
        context['current_search'] = self.request.GET.get('search', '')

        # Statistiques des conférences
        today = timezone.now().date()
        context['stats'] = {
            'total': Conference.objects.count(),
            'upcoming': Conference.objects.filter(start_date__gt=today).count(),
            'ongoing': Conference.objects.filter(start_date__lte=today, end_date__gte=today).count(),
            'past': Conference.objects.filter(end_date__lt=today).count(),
        }

        return context
