from django.db import models
from django.contrib.auth.models import AbstractUser

class Participant(AbstractUser):
    cin = models.CharField(max_length=8, unique=True)

    CHOICE = (
        ('ETUDIANT', 'Etudiant'),
        ('CHERCHEUR', 'Chercheur'),
        ('ENSEIGNANT', 'Enseignant'),
        ('DOCTE<PERSON>', 'Docteur'),
    )
    participant_category = models.CharField('category', max_length=100, choices=CHOICE)

    # USERNAME_FIELD is already set to 'username' in AbstractUser



