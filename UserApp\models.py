from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
import re

def validate_letters(value):
    if not re.match(r'^[a-zA-Z\s]+$', value):
        raise ValidationError('This field should only contain letters and spaces.')

def email_validator(value):
    if not value.endswith('@esprit.tn'):
        raise ValidationError('Invalid email address. Only @esprit.tn addresses are allowed.')

class Participant(AbstractUser):
    digitOnly = RegexValidator(r'^\d{8}$', 'This field must contain exactly 8 digits')
    cin = models.CharField(max_length=8, unique=True, validators=[digitOnly])
    email = models.EmailField(validators=[email_validator], unique=True, max_length=254)
    first_name = models.CharField(max_length=250, validators=[validate_letters])
    last_name = models.CharField(max_length=250, validators=[validate_letters])
    username = models.Char<PERSON>ield(unique=True, max_length=150)

    CHOICE = (
        ('ETUDIANT', 'Etudiant'),
        ('CHERCHEUR', 'Chercheur'),
        ('ENSEIGNANT', 'Enseignant'),
        ('DOCTEUR', 'Docteur'),
    )
    participant_category = models.CharField('category', max_length=100, choices=CHOICE)

    # USERNAME_FIELD is already set to 'username' in AbstractUser

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.cin})"



