<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - Gestion des Conférences</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .register-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .register-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .register-header h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .register-header p {
            color: #666;
            font-size: 1.1em;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }

        .auth-links {
            text-align: center;
            margin-top: 20px;
        }

        .auth-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .auth-links a:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        .messages {
            margin-bottom: 20px;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .errorlist {
            color: #dc3545;
            font-size: 0.9em;
            margin-top: 5px;
            list-style: none;
        }

        .errorlist li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .register-container {
                padding: 30px 20px;
            }
            
            .register-header h1 {
                font-size: 2em;
            }
        }

        .register-container {
            opacity: 0;
            transform: translateY(20px);
            animation: slideUp 0.6s ease forwards;
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h1>📝 Inscription</h1>
            <p>Créez votre compte pour accéder aux conférences</p>
        </div>

        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <form method="post">
            {% csrf_token %}
            
            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.first_name.id_for_label }}">Prénom</label>
                    {{ form.first_name }}
                    {% if form.first_name.errors %}
                        <ul class="errorlist">
                            {% for error in form.first_name.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.last_name.id_for_label }}">Nom</label>
                    {{ form.last_name }}
                    {% if form.last_name.errors %}
                        <ul class="errorlist">
                            {% for error in form.last_name.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>
            </div>

            <div class="form-group">
                <label for="{{ form.username.id_for_label }}">Nom d'utilisateur</label>
                {{ form.username }}
                {% if form.username.errors %}
                    <ul class="errorlist">
                        {% for error in form.username.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.email.id_for_label }}">Email (@esprit.tn)</label>
                {{ form.email }}
                {% if form.email.errors %}
                    <ul class="errorlist">
                        {% for error in form.email.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.cin.id_for_label }}">CIN (8 chiffres)</label>
                    {{ form.cin }}
                    {% if form.cin.errors %}
                        <ul class="errorlist">
                            {% for error in form.cin.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.participant_category.id_for_label }}">Catégorie</label>
                    {{ form.participant_category }}
                    {% if form.participant_category.errors %}
                        <ul class="errorlist">
                            {% for error in form.participant_category.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.password.id_for_label }}">Mot de passe</label>
                    {{ form.password }}
                    {% if form.password.errors %}
                        <ul class="errorlist">
                            {% for error in form.password.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.password_confirm.id_for_label }}">Confirmer le mot de passe</label>
                    {{ form.password_confirm }}
                    {% if form.password_confirm.errors %}
                        <ul class="errorlist">
                            {% for error in form.password_confirm.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>
            </div>

            {% if form.non_field_errors %}
                <ul class="errorlist">
                    {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}

            <button type="submit" class="btn">
                ✨ Créer mon compte
            </button>
        </form>

        <div class="auth-links">
            <p>Déjà un compte ? <a href="{% url 'login' %}">Se connecter</a></p>
        </div>
    </div>
</body>
</html>
