<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> une Conférence</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .form-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 1.1em;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #f8f9fa;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
            transform: translateY(-2px);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            min-width: 150px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            box-shadow: 0 4px 15px rgba(108,117,125,0.3);
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108,117,125,0.4);
        }

        .error {
            color: #dc3545;
            font-size: 0.9em;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .form-container {
                padding: 25px;
            }
            
            .form-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .btn {
                width: 100%;
            }
        }

        .form-container {
            opacity: 0;
            transform: translateY(20px);
            animation: slideUp 0.6s ease forwards;
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>➕ Créer une Conférence</h1>
            <p>Ajouter un nouvel événement</p>
        </div>

        <div class="form-container">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="form-group">
                    {{ form.title.label_tag }}
                    {{ form.title }}
                    {% if form.title.errors %}
                        <div class="error">{{ form.title.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.description.label_tag }}
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="error">{{ form.description.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.category.label_tag }}
                    {{ form.category }}
                    {% if form.category.errors %}
                        <div class="error">{{ form.category.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.location.label_tag }}
                    {{ form.location }}
                    {% if form.location.errors %}
                        <div class="error">{{ form.location.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.start_date.label_tag }}
                    {{ form.start_date }}
                    {% if form.start_date.errors %}
                        <div class="error">{{ form.start_date.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.end_date.label_tag }}
                    {{ form.end_date }}
                    {% if form.end_date.errors %}
                        <div class="error">{{ form.end_date.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.price.label_tag }}
                    {{ form.price }}
                    {% if form.price.errors %}
                        <div class="error">{{ form.price.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.capacity.label_tag }}
                    {{ form.capacity }}
                    {% if form.capacity.errors %}
                        <div class="error">{{ form.capacity.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.program.label_tag }}
                    {{ form.program }}
                    {% if form.program.errors %}
                        <div class="error">{{ form.program.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-actions">
                    <a href="{% url 'conference_class_list' %}" class="btn btn-secondary">
                        ← Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        ✅ Créer la Conférence
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
