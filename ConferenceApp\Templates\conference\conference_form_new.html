<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> une Conférence</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 900px;
        margin: 0 auto;
      }

      .header {
        text-align: center;
        margin-bottom: 40px;
        color: white;
      }

      .header h1 {
        font-size: 3em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .header p {
        font-size: 1.2em;
        opacity: 0.9;
      }

      .form-container {
        background: white;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
      }

      .form-container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
      }

      .progress-bar {
        width: 100%;
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        margin-bottom: 30px;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #007bff, #28a745);
        width: 0%;
        transition: width 0.3s ease;
      }

      .form-section {
        margin-bottom: 30px;
      }

      .section-title {
        font-size: 1.3em;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f1f3f4;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 25px;
        margin-bottom: 20px;
      }

      .form-group {
        display: flex;
        flex-direction: column;
      }

      .form-group.full-width {
        grid-column: 1 / -1;
      }

      .form-group label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        font-size: 1em;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        padding: 15px;
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        font-size: 1em;
        transition: all 0.3s ease;
        background: #f8f9fa;
        font-family: inherit;
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #007bff;
        background: white;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        transform: translateY(-2px);
      }

      .form-group textarea {
        resize: vertical;
        min-height: 120px;
      }

      .file-input-wrapper {
        position: relative;
        overflow: hidden;
        display: inline-block;
        width: 100%;
      }

      .file-input-wrapper input[type="file"] {
        position: absolute;
        left: -9999px;
      }

      .file-input-label {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 15px;
        border: 2px dashed #007bff;
        border-radius: 12px;
        background: #f8f9ff;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #007bff;
        font-weight: 500;
      }

      .file-input-label:hover {
        background: #e3f2fd;
        border-color: #0056b3;
      }

      .file-selected {
        background: #d4edda !important;
        border-color: #28a745 !important;
        color: #155724 !important;
      }

      .form-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 40px;
        flex-wrap: wrap;
      }

      .btn {
        padding: 15px 30px;
        border: none;
        border-radius: 25px;
        font-size: 1.1em;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        min-width: 150px;
        justify-content: center;
      }

      .btn-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
      }

      .btn-secondary:hover {
        background: #545b62;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
      }

      .error-message {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
      }

      @media (max-width: 768px) {
        .header h1 {
          font-size: 2em;
        }

        .form-container {
          padding: 25px;
        }

        .form-grid {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .form-actions {
          flex-direction: column;
          align-items: stretch;
        }

        .btn {
          width: 100%;
        }
      }

      .form-section {
        opacity: 0;
        transform: translateY(20px);
        animation: slideUp 0.5s ease forwards;
      }

      @keyframes slideUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .form-section:nth-child(1) {
        animation-delay: 0.1s;
      }
      .form-section:nth-child(2) {
        animation-delay: 0.3s;
      }
      .form-section:nth-child(3) {
        animation-delay: 0.5s;
      }
      .form-section:nth-child(4) {
        animation-delay: 0.7s;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🎯 Créer une Conférence</h1>
        <p>Organisez votre événement exceptionnel</p>
      </div>

      <div class="form-container">
        <div class="progress-bar">
          <div
            class="progress-fill"
            id="progressFill"></div>
        </div>

        <form
          method="post"
          enctype="multipart/form-data"
          id="conferenceForm">
          {% csrf_token %}

          <!-- Section Informations Générales -->
          <div class="form-section">
            <h3 class="section-title">📝 Informations Générales</h3>
            <div class="form-grid">
              <div class="form-group full-width">
                {{ form.title.label_tag }} {{ form.title }} {% if
                form.title.errors %}
                <div class="error-message">⚠️ {{ form.title.errors.0 }}</div>
                {% endif %}
              </div>
              <div class="form-group full-width">
                {{ form.description.label_tag }} {{ form.description }} {% if
                form.description.errors %}
                <div class="error-message">
                  ⚠️ {{ form.description.errors.0 }}
                </div>
                {% endif %}
              </div>
              <div class="form-group">
                {{ form.category.label_tag }} {{ form.category }} {% if
                form.category.errors %}
                <div class="error-message">⚠️ {{ form.category.errors.0 }}</div>
                {% endif %}
              </div>
              <div class="form-group">
                {{ form.location.label_tag }} {{ form.location }} {% if
                form.location.errors %}
                <div class="error-message">⚠️ {{ form.location.errors.0 }}</div>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- Section Dates et Horaires -->
          <div class="form-section">
            <h3 class="section-title">📅 Dates et Horaires</h3>
            <div class="form-grid">
              <div class="form-group">
                {{ form.start_date.label_tag }} {{ form.start_date }} {% if
                form.start_date.errors %}
                <div class="error-message">
                  ⚠️ {{ form.start_date.errors.0 }}
                </div>
                {% endif %}
              </div>
              <div class="form-group">
                {{ form.end_date.label_tag }} {{ form.end_date }} {% if
                form.end_date.errors %}
                <div class="error-message">⚠️ {{ form.end_date.errors.0 }}</div>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- Section Tarification et Capacité -->
          <div class="form-section">
            <h3 class="section-title">💰 Tarification et Capacité</h3>
            <div class="form-grid">
              <div class="form-group">
                {{ form.price.label_tag }} {{ form.price }} {% if
                form.price.errors %}
                <div class="error-message">⚠️ {{ form.price.errors.0 }}</div>
                {% endif %}
              </div>
              <div class="form-group">
                {{ form.capacity.label_tag }} {{ form.capacity }} {% if
                form.capacity.errors %}
                <div class="error-message">⚠️ {{ form.capacity.errors.0 }}</div>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- Section Programme -->
          <div class="form-section">
            <h3 class="section-title">📋 Programme (Optionnel)</h3>
            <div class="form-group full-width">
              <div class="file-input-wrapper">
                {{ form.program }}
                <label
                  for="{{ form.program.id_for_label }}"
                  class="file-input-label"
                  id="fileLabel">
                  📄 Choisir un fichier PDF
                  <small>(Optionnel)</small>
                </label>
              </div>
              {% if form.program.errors %}
              <div class="error-message">⚠️ {{ form.program.errors.0 }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-actions">
            <a
              href="{% url 'conference_class_list' %}"
              class="btn btn-secondary">
              ← Annuler
            </a>
            <button
              type="submit"
              class="btn btn-primary">
              ✅ Créer la Conférence
            </button>
          </div>
        </form>
      </div>
    </div>

    <script>
      // Gestion du fichier
      const fileInput = document.getElementById(
        "{{ form.program.id_for_label }}"
      );
      const fileLabel = document.getElementById("fileLabel");

      if (fileInput) {
        fileInput.addEventListener("change", function (e) {
          if (e.target.files.length > 0) {
            const fileName = e.target.files[0].name;
            fileLabel.innerHTML = `✅ ${fileName}`;
            fileLabel.classList.add("file-selected");
          } else {
            fileLabel.innerHTML =
              "📄 Choisir un fichier PDF <small>(Optionnel)</small>";
            fileLabel.classList.remove("file-selected");
          }
        });
      }

      // Barre de progression basée sur les champs remplis
      function updateProgress() {
        const form = document.getElementById("conferenceForm");
        const inputs = form.querySelectorAll(
          "input[required], select[required], textarea[required]"
        );
        const filled = Array.from(inputs).filter(
          (input) => input.value.trim() !== ""
        ).length;
        const progress = (filled / inputs.length) * 100;
        document.getElementById("progressFill").style.width = progress + "%";
      }

      // Écouter les changements dans le formulaire
      document
        .getElementById("conferenceForm")
        .addEventListener("input", updateProgress);
      document
        .getElementById("conferenceForm")
        .addEventListener("change", updateProgress);

      // Initialiser la barre de progression
      updateProgress();

      // Animation de focus sur les champs
      const inputs = document.querySelectorAll("input, select, textarea");
      inputs.forEach((input) => {
        input.addEventListener("focus", function () {
          this.parentElement.style.transform = "scale(1.02)";
        });

        input.addEventListener("blur", function () {
          this.parentElement.style.transform = "scale(1)";
        });
      });
    </script>
  </body>
</html>
