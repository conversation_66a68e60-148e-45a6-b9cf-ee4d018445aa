from django.db import models
from django.utils import timezone
from UserApp.models import Participant
from django.core.exceptions import ValidationError
import re

def validate_letters(value):
    if not re.match(r'^[a-zA-Z ]+$', value):
        raise ValidationError('This field should only contain letters and spaces.')

def validate_letters_minlen(value):
    """Valide que le champ contient seulement des lettres et espaces avec minimum 5 caractères"""
    if not re.match(r'^[a-zA-Z ]+$', value):
        raise ValidationError('This field should only contain letters and spaces.')

    # Compter seulement les lettres (sans les espaces)
    letters_only = re.sub(r'[^a-zA-Z]', '', value)
    if len(letters_only) < 5:
        raise ValidationError('This field must contain at least 5 letters.')

def validate_start_date(value):
    if value < timezone.now():
        raise ValidationError('Start date cannot be in the past.')

class Category(models.Model):
    title = models.CharField(max_length=200, validators=[validate_letters])

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Categories"

    def __str__(self):
        return self.title

class Conference(models.Model):
    title = models.CharField(max_length=250, validators=[validate_letters_minlen])
    description = models.TextField(blank=True)
    start_date = models.DateTimeField(default=timezone.now, validators=[validate_start_date])
    end_date = models.DateTimeField(default=timezone.now)
    location = models.CharField(max_length=250)
    price = models.FloatField()
    capacity = models.IntegerField()
    program = models.FileField(upload_to='files/')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='conferences')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

class Reservation(models.Model):
    confirmed = models.BooleanField(default=False)
    reservation_date = models.DateTimeField(default=timezone.now)
    conference = models.ForeignKey(Conference, on_delete=models.CASCADE)
    participant = models.ForeignKey(Participant, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('conference', 'participant')

    def __str__(self):
        return f"{self.participant} - {self.conference} ({'Confirmed' if self.confirmed else 'Pending'})"

