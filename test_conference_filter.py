#!/usr/bin/env python
"""
Script de test pour le filtre de conférences par date
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from django.utils import timezone
from ConferenceApp.models import Category, Conference
from ConferenceApp.admin import ConferenceDateFilter

def create_test_conferences():
    """Crée des conférences de test avec différentes dates"""
    
    # Créer une catégorie de test
    category, created = Category.objects.get_or_create(
        title="Test Category",
        defaults={'title': "Test Category"}
    )
    
    today = timezone.now().date()
    
    # Supprimer les anciennes conférences de test
    Conference.objects.filter(title__startswith="Test Conference").delete()
    
    # Créer des conférences de test
    conferences = [
        # Conférence passée (terminée)
        {
            'title': 'Test Conference Past',
            'start_date': today - timedelta(days=5),
            'end_date': today - timedelta(days=3),
            'status': 'past'
        },
        # Conférence en cours (ongoing)
        {
            'title': 'Test Conference Ongoing',
            'start_date': today - timedelta(days=2),
            'end_date': today + timedelta(days=2),
            'status': 'ongoing'
        },
        # Conférence future (upcoming)
        {
            'title': 'Test Conference Future',
            'start_date': today + timedelta(days=3),
            'end_date': today + timedelta(days=5),
            'status': 'upcoming'
        },
        # Conférence aujourd'hui (today)
        {
            'title': 'Test Conference Today',
            'start_date': today,
            'end_date': today,
            'status': 'today'
        },
    ]
    
    created_conferences = []
    for conf_data in conferences:
        conference = Conference.objects.create(
            title=conf_data['title'],
            description=f"Description for {conf_data['title']}",
            start_date=conf_data['start_date'],
            end_date=conf_data['end_date'],
            location="Test Location",
            price=100.0,
            capacity=50,
            category=category
        )
        created_conferences.append((conference, conf_data['status']))
        print(f"✅ Créé: {conference.title} ({conf_data['status']})")
    
    return created_conferences

def test_conference_filter():
    """Teste le filtre de conférences par date"""
    
    print("🧪 Test du filtre de conférences par date")
    print("=" * 50)
    
    # Créer des conférences de test
    conferences = create_test_conferences()
    
    # Tester chaque filtre
    filters_to_test = ['past', 'upcoming', 'ongoing', 'today']
    
    for filter_value in filters_to_test:
        print(f"\n🔍 Test du filtre: {filter_value}")
        print("-" * 30)
        
        # Simuler le filtre
        queryset = Conference.objects.all()
        today = timezone.now().date()
        
        if filter_value == 'past':
            filtered_queryset = queryset.filter(end_date__lt=today)
        elif filter_value == 'upcoming':
            filtered_queryset = queryset.filter(start_date__gt=today)
        elif filter_value == 'ongoing':
            filtered_queryset = queryset.filter(start_date__lte=today, end_date__gte=today)
        elif filter_value == 'today':
            filtered_queryset = queryset.filter(start_date=today)
        
        # Afficher les résultats
        results = filtered_queryset.filter(title__startswith="Test Conference")
        print(f"Conférences trouvées: {results.count()}")
        
        for conf in results:
            print(f"  - {conf.title}")
            print(f"    Du {conf.start_date} au {conf.end_date}")
    
    print(f"\n📊 Résumé:")
    print(f"Date d'aujourd'hui: {today}")
    print(f"Total conférences de test: {Conference.objects.filter(title__startswith='Test Conference').count()}")

def test_filter_logic():
    """Teste la logique spécifique du filtre ongoing"""
    
    print("\n🎯 Test spécifique du filtre 'ongoing'")
    print("=" * 40)
    
    today = timezone.now().date()
    
    # Test de la logique ongoing
    ongoing_conferences = Conference.objects.filter(
        start_date__lte=today, 
        end_date__gte=today,
        title__startswith="Test Conference"
    )
    
    print(f"Conférences en cours (start_date <= {today} <= end_date):")
    for conf in ongoing_conferences:
        print(f"  ✅ {conf.title}")
        print(f"     Début: {conf.start_date}")
        print(f"     Fin: {conf.end_date}")
        print(f"     En cours: {conf.start_date <= today <= conf.end_date}")

if __name__ == "__main__":
    test_conference_filter()
    test_filter_logic()
