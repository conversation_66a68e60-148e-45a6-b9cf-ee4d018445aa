#!/usr/bin/env python
"""
Script complet pour gérer les utilisateurs admin Django
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist

def main_menu():
    """Menu principal de gestion des utilisateurs admin"""
    
    User = get_user_model()
    
    print("🔧 Gestionnaire des utilisateurs admin Django")
    print("=" * 50)
    
    while True:
        print("\n📋 Options disponibles :")
        print("1. 👀 Voir tous les utilisateurs admin")
        print("2. ➕ Créer un nouvel utilisateur admin")
        print("3. 🔑 Changer le mot de passe d'un admin")
        print("4. ✏️  Modifier les informations d'un admin")
        print("5. 🗑️  Supprimer un utilisateur admin")
        print("6. 🔄 Réinitialiser complètement un admin")
        print("7. 🚪 Quitter")
        
        try:
            choice = input("\n➤ Choisissez une option (1-7) : ").strip()
            
            if choice == '1':
                list_admin_users()
            elif choice == '2':
                create_admin_user()
            elif choice == '3':
                change_admin_password()
            elif choice == '4':
                modify_admin_info()
            elif choice == '5':
                delete_admin_user()
            elif choice == '6':
                reset_admin_user()
            elif choice == '7':
                print("👋 Au revoir !")
                break
            else:
                print("❌ Option invalide. Choisissez entre 1 et 7.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Au revoir !")
            break

def list_admin_users():
    """Affiche tous les utilisateurs admin"""
    User = get_user_model()
    admins = User.objects.filter(is_superuser=True)
    
    print("\n👥 Utilisateurs admin existants :")
    print("-" * 40)
    
    if admins.exists():
        for i, user in enumerate(admins, 1):
            status = "🟢 Actif" if user.is_active else "🔴 Inactif"
            print(f"{i}. {user.username}")
            print(f"   📧 Email: {user.email}")
            print(f"   📅 Créé: {user.date_joined.strftime('%d/%m/%Y %H:%M')}")
            print(f"   📊 Statut: {status}")
            print()
    else:
        print("⚠️  Aucun utilisateur admin trouvé.")

def create_admin_user():
    """Crée un nouvel utilisateur admin"""
    print("\n➕ Création d'un nouvel utilisateur admin")
    print("-" * 40)
    
    try:
        User = get_user_model()
        
        username = input("👤 Username : ").strip()
        if not username:
            print("❌ Le nom d'utilisateur ne peut pas être vide.")
            return
            
        # Vérifier si l'utilisateur existe déjà
        if User.objects.filter(username=username).exists():
            print(f"❌ L'utilisateur '{username}' existe déjà.")
            return
            
        email = input("📧 Email (@esprit.tn requis) : ").strip()
        if not email.endswith('@esprit.tn'):
            print("❌ L'email doit se terminer par @esprit.tn")
            return
            
        password = input("🔑 Mot de passe : ").strip()
        if len(password) < 3:
            print("❌ Le mot de passe doit contenir au moins 3 caractères.")
            return
        
        first_name = input("👤 Prénom (optionnel) : ").strip()
        last_name = input("👤 Nom (optionnel) : ").strip()
        
        # Créer l'utilisateur
        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name
        )
        
        print(f"\n✅ Utilisateur admin créé avec succès!")
        print(f"👤 Username: {username}")
        print(f"📧 Email: {email}")
        print(f"🌐 Connexion: http://127.0.0.1:8000/admin/")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création : {e}")

def change_admin_password():
    """Change le mot de passe d'un utilisateur admin"""
    User = get_user_model()
    admins = User.objects.filter(is_superuser=True)
    
    if not admins.exists():
        print("⚠️  Aucun utilisateur admin trouvé.")
        return
    
    print("\n🔑 Changement de mot de passe")
    print("-" * 40)
    
    # Afficher les utilisateurs
    for i, user in enumerate(admins, 1):
        print(f"{i}. {user.username} ({user.email})")
    
    try:
        choice = int(input("\n➤ Choisissez l'utilisateur (numéro) : ")) - 1
        if 0 <= choice < len(admins):
            user = admins[choice]
            new_password = input(f"🔑 Nouveau mot de passe pour {user.username} : ").strip()
            
            if len(new_password) < 3:
                print("❌ Le mot de passe doit contenir au moins 3 caractères.")
                return
                
            user.set_password(new_password)
            user.save()
            
            print(f"✅ Mot de passe changé pour {user.username}!")
        else:
            print("❌ Choix invalide.")
    except (ValueError, IndexError):
        print("❌ Choix invalide.")

def reset_admin_user():
    """Réinitialise complètement un utilisateur admin"""
    User = get_user_model()
    admins = User.objects.filter(is_superuser=True)
    
    if not admins.exists():
        print("⚠️  Aucun utilisateur admin trouvé.")
        return
    
    print("\n🔄 Réinitialisation complète d'un utilisateur admin")
    print("-" * 50)
    
    # Afficher les utilisateurs
    for i, user in enumerate(admins, 1):
        print(f"{i}. {user.username} ({user.email})")
    
    try:
        choice = int(input("\n➤ Choisissez l'utilisateur à réinitialiser (numéro) : ")) - 1
        if 0 <= choice < len(admins):
            user = admins[choice]
            
            print(f"\n⚠️  Vous allez réinitialiser l'utilisateur : {user.username}")
            confirm = input("Êtes-vous sûr ? (oui/non) : ").strip().lower()
            
            if confirm in ['oui', 'o', 'yes', 'y']:
                # Réinitialiser les informations
                new_username = input(f"👤 Nouveau username (actuel: {user.username}) : ").strip() or user.username
                new_email = input(f"📧 Nouvel email (actuel: {user.email}) : ").strip() or user.email
                new_password = input("🔑 Nouveau mot de passe : ").strip()
                
                if not new_email.endswith('@esprit.tn'):
                    print("❌ L'email doit se terminer par @esprit.tn")
                    return
                
                if len(new_password) < 3:
                    print("❌ Le mot de passe doit contenir au moins 3 caractères.")
                    return
                
                # Appliquer les changements
                user.username = new_username
                user.email = new_email
                user.set_password(new_password)
                user.is_active = True
                user.save()
                
                print(f"\n✅ Utilisateur {new_username} réinitialisé avec succès!")
                print(f"👤 Username: {new_username}")
                print(f"📧 Email: {new_email}")
                print(f"🔑 Nouveau mot de passe: {new_password}")
            else:
                print("❌ Opération annulée.")
        else:
            print("❌ Choix invalide.")
    except (ValueError, IndexError):
        print("❌ Choix invalide.")

def modify_admin_info():
    """Modifie les informations d'un utilisateur admin"""
    print("\n✏️  Modification des informations admin")
    print("-" * 40)
    print("(Fonctionnalité à implémenter)")

def delete_admin_user():
    """Supprime un utilisateur admin"""
    print("\n🗑️  Suppression d'un utilisateur admin")
    print("-" * 40)
    print("(Fonctionnalité à implémenter pour sécurité)")

if __name__ == "__main__":
    main_menu()
