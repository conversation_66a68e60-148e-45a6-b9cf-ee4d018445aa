#!/usr/bin/env python
"""
Script de test pour valider l'extension PDF
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from ConferenceApp.models import validate_pdf_extension
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile

def test_pdf_validation():
    print("🧪 Test du validateur d'extension PDF")
    print("=" * 50)
    
    # Simuler des fichiers avec différentes extensions
    test_files = [
        # Fichiers VALIDES (PDF)
        ("document.pdf", True),
        ("rapport.PDF", True),  # Test insensible à la casse
        ("presentation.Pdf", True),
        ("guide.pDf", True),
        
        # Fichiers INVALIDES (non-PDF)
        ("document.doc", False),
        ("image.jpg", False),
        ("video.mp4", False),
        ("archive.zip", False),
        ("text.txt", False),
        ("spreadsheet.xlsx", False),
        ("presentation.ppt", False),
        ("fichier_sans_extension", False),
    ]
    
    print("✅ Tests de fichiers VALIDES (extension PDF) :")
    for filename, should_pass in test_files:
        if should_pass:
            try:
                # Créer un fichier simulé
                fake_file = SimpleUploadedFile(filename, b"fake content")
                validate_pdf_extension(fake_file)
                print(f"   ✅ '{filename}' - ACCEPTÉ")
            except ValidationError as e:
                print(f"   ❌ '{filename}' - ERREUR: {e}")
    
    print("\n❌ Tests de fichiers INVALIDES (extension non-PDF) :")
    for filename, should_pass in test_files:
        if not should_pass:
            try:
                # Créer un fichier simulé
                fake_file = SimpleUploadedFile(filename, b"fake content")
                validate_pdf_extension(fake_file)
                print(f"   ❌ '{filename}' - ERREUR: Devrait être rejeté!")
            except ValidationError as e:
                print(f"   ✅ '{filename}' - CORRECTEMENT REJETÉ: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Test terminé !")
    print("📝 Le validateur vérifie que l'extension du fichier est '.pdf'")
    print("🔤 La vérification est insensible à la casse (PDF, pdf, Pdf, etc.)")

def test_validator_function_directly():
    """Test direct de la fonction validate_pdf_extension"""
    print("\n🔧 Test direct de la fonction validate_pdf_extension")
    print("-" * 50)
    
    from ConferenceApp.models import validate_pdf_extension
    
    # Test avec un fichier PDF valide
    try:
        pdf_file = SimpleUploadedFile("test.pdf", b"fake pdf content")
        validate_pdf_extension(pdf_file)
        print("✅ Fichier PDF valide accepté")
    except ValidationError as e:
        print(f"❌ Erreur inattendue: {e}")
    
    # Test avec un fichier non-PDF
    try:
        doc_file = SimpleUploadedFile("test.doc", b"fake doc content")
        validate_pdf_extension(doc_file)
        print("❌ Fichier non-PDF accepté (erreur!)")
    except ValidationError as e:
        print(f"✅ Fichier non-PDF correctement rejeté: {e}")

if __name__ == "__main__":
    test_pdf_validation()
    test_validator_function_directly()
