# Script pour supprimer complètement les warnings Django
import warnings
import logging
import os
import sys

def suppress_django_warnings():
    """Supprime toutes les warnings du serveur de développement Django"""
    
    # Supprimer toutes les warnings liées au serveur de développement
    warnings.filterwarnings('ignore', message='This is a development server')
    warnings.filterwarnings('ignore', message='.*development server.*')
    warnings.filterwarnings('ignore', message='.*Do not use it in a production setting.*')
    warnings.filterwarnings('ignore', message='.*Use a production WSGI or ASGI server instead.*')
    warnings.filterwarnings('ignore', message='.*For more information on production servers.*')
    warnings.filterwarnings('ignore', category=UserWarning, module='django.core.management.commands.runserver')
    warnings.filterwarnings('ignore', category=UserWarning, module='django.core.management.base')
    
    # Supprimer les logs de warning
    logging.getLogger('django.server').setLevel(logging.ERROR)
    logging.getLogger('django.core.management.commands.runserver').setLevel(logging.ERROR)
    logging.getLogger('django.core.management.base').setLevel(logging.ERROR)
    
    # Variables d'environnement pour supprimer les warnings
    os.environ['PYTHONWARNINGS'] = 'ignore::UserWarning:django.core.management.commands.runserver'
    os.environ['DJANGO_SUPPRESS_WARNINGS'] = '1'

# Appliquer automatiquement
suppress_django_warnings()
