#!/usr/bin/env python
"""
Script de test pour valider la fonction validate_letters_minlen
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from ConferenceApp.models import validate_letters
from django.core.exceptions import ValidationError

def test_validation():
    print("🧪 Test de la validation des titres de conférence")
    print("=" * 50)
    
    # Tests qui doivent PASSER
    valid_titles = [
        "Python Conference",      # 6 lettres + 10 lettres = 16 lettres ✅
        "Django Summit",          # 6 lettres + 6 lettres = 12 lettres ✅
        "AI Workshop",           # 2 lettres + 8 lettres = 10 lettres ✅
        "Tech Event",            # 4 lettres + 5 lettres = 9 lettres ✅
        "Hello World",           # 5 lettres + 5 lettres = 10 lettres ✅
        "ABCDE",                 # 5 lettres exactement ✅
    ]
    
    # Tests qui doivent ÉCHOUER
    invalid_titles = [
        "AI",                    # 2 lettres seulement ❌
        "Tech",                  # 4 lettres seulement ❌
        "A B C D",               # 4 lettres seulement ❌
        "123 Conference",        # Contient des chiffres ❌
        "Tech@Event",            # Contient des caractères spéciaux ❌
        "",                      # Vide ❌
        "   ",                   # Seulement des espaces ❌
    ]
    
    print("✅ Tests qui doivent PASSER :")
    for title in valid_titles:
        try:
            validate_letters(title)
            print(f"   ✅ '{title}' - VALIDE")
        except ValidationError as e:
            print(f"   ❌ '{title}' - ERREUR: {e}")

    print("\n❌ Tests qui doivent ÉCHOUER :")
    for title in invalid_titles:
        try:
            validate_letters(title)
            print(f"   ❌ '{title}' - ERREUR: Devrait être invalide!")
        except ValidationError as e:
            print(f"   ✅ '{title}' - CORRECTEMENT REJETÉ: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Test terminé !")

if __name__ == "__main__":
    test_validation()
