<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>Inscription - Gestion des Conférences</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .container {
        max-width: 500px;
        width: 100%;
        background: white;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
      }

      .container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, #667eea, #764ba2);
      }

      h1 {
        color: #667eea;
        font-size: 2.5em;
        margin-bottom: 30px;
        text-align: center;
      }

      /* Style pour {{ form.as_p }} */
      form p {
        margin-bottom: 20px;
      }

      form p label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 600;
        font-size: 0.95em;
      }

      form p input,
      form p select {
        width: 100%;
        padding: 15px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        font-size: 1em;
        transition: all 0.3s ease;
        background: #f8f9fa;
        font-family: inherit;
      }

      form p input:focus,
      form p select:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      button[type="submit"] {
        width: 100%;
        padding: 15px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 12px;
        font-size: 1.1em;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 10px;
      }

      button[type="submit"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      .errorlist {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 5px;
        list-style: none;
        padding: 0;
      }

      .errorlist li {
        margin-bottom: 5px;
        padding: 5px 10px;
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 6px;
      }

      @media (max-width: 480px) {
        .container {
          padding: 30px 20px;
          margin: 10px;
        }

        h1 {
          font-size: 2em;
        }
      }

      .container {
        opacity: 0;
        transform: translateY(20px);
        animation: slideUp 0.6s ease forwards;
      }

      @keyframes slideUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>📝 Inscription</h1>
      <form method="post">
        {% csrf_token %} {{ form.as_p }}
        <button type="submit">S'inscrire</button>
      </form>
    </div>
  </body>
</html>
