from django.contrib import admin
from django.utils import timezone
from .models import Category, Conference, Reservation


class ConferenceDateFilter(admin.SimpleListFilter):
    title = 'Conference Date'
    parameter_name = 'Conference_date'

    def lookups(self, request, model_admin):
        return (
            ('past', 'Past Conferences'),
            ('upcoming', 'Upcoming Conferences'),
            ('ongoing', 'Ongoing Conferences'),
            ('today', 'Today Conferences'),
        )

    def queryset(self, request, queryset):
        today = timezone.now().date()
        if self.value() == 'past':
            return queryset.filter(end_date__lt=today)
        if self.value() == 'upcoming':
            return queryset.filter(start_date__gt=today)
        if self.value() == 'ongoing':
            return queryset.filter(start_date__lte=today, end_date__gte=today)
        if self.value() == 'today':
            return queryset.filter(start_date=today)
        return queryset


class ParticipantFilter(admin.SimpleListFilter):
    title = 'Participant Status'
    parameter_name = 'participant_status'

    def lookups(self, request, model_admin):
        return (
            ('No', 'No Reservation'),
            ('Yes', 'There is a Reservation'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'No':
            return queryset.filter(reservation__isnull=True)
        elif self.value() == 'Yes':
            return queryset.filter(reservation__isnull=False).distinct()
        else:
            return queryset

class CategoryAdmin(admin.ModelAdmin):
    search_fields = ('title',)

class ConferenceAdmin(admin.ModelAdmin):
    list_display = ('title', 'location', 'start_date', 'category', 'price', 'capacity')
    search_fields = ('title', 'location')
    date_hierarchy = 'start_date'
    list_per_page = 10
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('start_date', 'title')
    list_filter = ('category', ConferenceDateFilter)
    
    fieldsets = (
        ( 'description',{fields: ('title', 'description')}, 
        {'Horaire', {fields: ('start_date', 'end_date')}},
        {'Location', {fields: ('location', 'price', 'capacity')}},
        {'Category', {fields: ('category',)}},
        {'Program', {fields: ('program',)}},
        {'Timestamps', {fields: ('created_at', 'updated_at')}}

        )
    )

class ReservationAdmin(admin.ModelAdmin):
    list_display = ('participant', 'conference', 'confirmed', 'reservation_date')
    list_filter = ('confirmed', 'reservation_date', ParticipantFilter)
    search_fields = ('participant__username', 'conference__title')

admin.site.register(Category, CategoryAdmin)
admin.site.register(Conference, ConferenceAdmin)
admin.site.register(Reservation)

