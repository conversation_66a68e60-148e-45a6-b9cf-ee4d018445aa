Stack trace:
Frame         Function      Args
0007FFFF7600  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF6500) msys-2.0.dll+0x1FE8E
0007FFFF7600  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF78D8) msys-2.0.dll+0x67F9
0007FFFF7600  000210046832 (000210286019, 0007FFFF74B8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7600  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7600  000210068E24 (0007FFFF7610, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF78E0  00021006A225 (0007FFFF7610, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8F3B20000 ntdll.dll
7FF8F3920000 KERNEL32.DLL
7FF8F10B0000 KERNELBASE.dll
7FF8F2140000 USER32.dll
7FF8F1080000 win32u.dll
7FF8F38F0000 GDI32.dll
7FF8F16E0000 gdi32full.dll
7FF8F0C70000 msvcp_win.dll
7FF8F0F30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8F3730000 advapi32.dll
7FF8F2F70000 msvcrt.dll
7FF8F2090000 sechost.dll
7FF8F2720000 RPCRT4.dll
7FF8F0370000 CRYPTBASE.DLL
7FF8F1820000 bcryptPrimitives.dll
7FF8F2940000 IMM32.DLL
