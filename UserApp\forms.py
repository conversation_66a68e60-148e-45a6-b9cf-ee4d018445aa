from django import forms
from .models import Participant
class ParticipantCreationForm(form.ModelForm):
    class Meta:
        model = Participant
        fields = ['cin', 'username', 'email', 'first_name', 'last_name', 'password', 'participant_category',]
def save(self, commit=True):
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password"])
        if commit:
            user.save()
        return user
